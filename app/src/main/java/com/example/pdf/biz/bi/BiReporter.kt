package com.example.pdf.biz.bi

import com.example.pdf.serialization.toJson
import com.example.pdf.serialization.toObj
import com.example.pdf.crypt.networkDataDecrypt
import com.example.pdf.crypt.networkDataEncrypt
import com.example.pdf.crypt.requestBodyEncrypt
import com.example.pdf.kermit.debugLog
import com.example.pdf.network.NetworkConf
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first

class BiReporter(
  private val biApi: BiApi,
  private val biDao: BiDao,
) {
  @Suppress("PropertyName")
  val TAG = BiReporter.TAG

  companion object {
    const val TAG = "BiReporter"

    const val PATH_CREATE_EVENT = "hi/e"
    const val PATH_VALUE_EVENT = "hi/v"
    const val PATH_CK_CREATE_EVENT = "hi/ck/createEvent"
    const val HEADER_WB_DATA_KEY = "mot"
    const val HEADER_PKG_NAME_KEY = "mot-e"
    const val BODY_JSON_DATA_KEY = "data"

    suspend fun header(): Map<String, String>? {
      val biEventHeader = DeviceInfoReader.biEventHeader() ?: return null

      debugLog(tag = TAG) { "header() biEventHeader == $biEventHeader" }

      val encryptedHeaderJsonString =
        biEventHeader
          .toJson()
          .takeUnless { it.trim().isEmpty() }
          ?.networkDataEncrypt()
          ?: return null

      debugLog(tag = TAG) { "header() encryptedHeaderJsonString == $encryptedHeaderJsonString" }

      return buildMap {
        put("accept", "application/json")
        put("content-type", "application/json")
        put("api-version", "${NetworkConf.apiVersion}")
        put(HEADER_PKG_NAME_KEY, "com.wydevteam.mypdf")
        put(HEADER_WB_DATA_KEY, encryptedHeaderJsonString)
      }
    }
  }


  private val isHandlePendingEvents = MutableStateFlow(false)

  suspend fun handlePendingEvents() {
    if (isHandlePendingEvents.first()) return

    debugLog(tag = TAG) { "start handlePendingEvents()" }
    isHandlePendingEvents.emit(true)

    val pendingEvents = biDao.fetchAll()
    if (pendingEvents.isEmpty()) {
      isHandlePendingEvents.emit(false)
      debugLog(tag = TAG) { "finish handlePendingEvents()" }
      return
    }

    val header = header()
    if (header == null) {
      isHandlePendingEvents.emit(false)
      debugLog(tag = TAG) { "finish handlePendingEvents()" }
      return
    }

    val reportEvents = mutableListOf<BiRecordEvent?>()
    val reportValues = mutableListOf<BiRecordValue?>()

    pendingEvents.forEach { event ->
      when (event.path) {
        PATH_CREATE_EVENT -> reportEvents.add(event.eventJson.toObj())
        PATH_VALUE_EVENT -> reportValues.add(event.eventJson.toObj())
        else -> {}
      }
    }

    if (reportEvents.isNotEmpty()) {
      val createEventsResponse = try {
        biApi.report(
          path = PATH_CREATE_EVENT,
          headerMap = header,
          body = reportEvents.toList().toJson().requestBodyEncrypt().apply {
            debugLog(tag = TAG) { "eventJson: $this" }
          }
        )
      } catch (e: Exception) {
        e.printStackTrace()
        null
      }
      if (createEventsResponse?.isSuccessful == true) {
        biDao.deleteByPath(PATH_CREATE_EVENT)
      }
      debugLog(tag = TAG) { "createEvents response: $createEventsResponse" }
    }

    if (reportValues.isNotEmpty()) {
      val createValuesResponse = try {
        biApi.report(
          path = PATH_VALUE_EVENT,
          headerMap = header,
          body = reportValues.toList().toJson().requestBodyEncrypt().apply {
            debugLog(tag = TAG) { "eventJson: $this" }
          }
        )
      } catch (e: Exception) {
        e.printStackTrace()
        null
      }
      if (createValuesResponse?.isSuccessful == true) {
        biDao.deleteByPath(PATH_VALUE_EVENT)
      }
      debugLog(tag = TAG) { "createValues response: $createValuesResponse" }
    }

    isHandlePendingEvents.emit(false)
    debugLog(tag = TAG) { "finish handlePendingEvents()" }
  }

  suspend fun reportEvent(
    event: BiRecordEvent
  ): Boolean {
    val eventJson = event.toJson().takeUnless { it.trim().isEmpty() } ?: return false

    return reportOrSaveToPending(
      path = PATH_CREATE_EVENT,
      eventBodyJson = eventJson
    )
  }

  suspend fun recordValue(
    value: BiRecordValue
  ): Boolean {
    val valueJson = value.toJson().takeUnless { it.trim().isEmpty() } ?: return false

    return reportOrSaveToPending(
      path = PATH_VALUE_EVENT,
      eventBodyJson = valueJson
    )
  }

  suspend fun reportCkEvent(
    event: BiRecordCkEvent
  ): Boolean {
    val eventJson = event.toJson().takeUnless { it.trim().isEmpty() } ?: return false

    return reportOrSaveToPending(
      path = PATH_CK_CREATE_EVENT,
      eventBodyJson = eventJson
    )
  }

  private suspend fun reportOrSaveToPending(
    path: String,
    eventBodyJson: String,
    saveToPending: Boolean = true,
  ): Boolean {
    debugLog(tag = TAG) { "reportOrSaveToPending()" }

    val header = header()

    if (header == null) {
      debugLog(tag = TAG) { "reportOrSaveToPending() header == null" }

      if (saveToPending) {
        addToPending(
          path = path,
          eventBodyJson = eventBodyJson
        )
      }

      return false
    } else {
      val response = try {
        biApi.report(
          path = path,
          headerMap = header,
          body = eventBodyJson.requestBodyEncrypt().apply {
            debugLog(tag = TAG) {
              "eventJson: ${
                this.replace("{\"$BODY_JSON_DATA_KEY\":\"", "").replace("\"}", "")
                  .networkDataDecrypt()
              }"
            }
          }
        )
      } catch (e: Exception) {
        Firebase.crashlytics.recordException(e)
        e.printStackTrace()
        null
      }

      debugLog(tag = TAG) { "response: $response" }

      if (response?.isSuccessful != true && saveToPending) {
        addToPending(
          path = path,
          eventBodyJson = eventBodyJson
        )
      }

      @Suppress("NullableBooleanElvis")
      return response?.isSuccessful ?: false
    }
  }

  private fun addToPending(
    path: String,
    eventBodyJson: String
  ) {
    biDao.addPending(
      event = BiEventCacheEntity(
        path = path,
        eventJson = eventBodyJson
      )
    )

    debugLog(tag = TAG) { "addToPending()" }
  }
}