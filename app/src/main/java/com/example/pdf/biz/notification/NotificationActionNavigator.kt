package com.example.pdf.biz.notification

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.content.IntentCompat
import com.example.pdf.activityStack
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.fcm.cancelFcmServiceNotificationJob
import com.example.pdf.biz.fcm.isFcmServiceNotification
import com.example.pdf.finish
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.guia.ScreenNode
import com.example.pdf.kermit.debugLog
import com.example.pdf.top
import com.example.pdf.ui.activity.SignaturePadActivity
import com.example.pdf.ui.activity.SplashActivity
import com.example.pdf.ui.activity.SplashLaunchType
import com.example.pdf.ui.node.home.HomeNode
import com.roudikk.guia.extensions.popToRoot
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.setRoot
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.random.Random

object NotificationActionNavigator : KoinComponent {

  private const val TAG = "NotificationActionNavigator"
  private const val EXTRA_KEY_NOTI_NAV_SCREEN_NODE = "${TAG}_noti_nav_screen_node"
  private const val EXTRA_KEY_NOTI_ID = "${TAG}_noti_id"
  private const val EXTRA_KEY_CLICK_FOR_EVENT_RECORD = "${TAG}_click_for_event_record"


  private val context: Context by inject()
  private val splashController: SplashController by inject()


  private val androidNotificationManager
    get() = context.getSystemService(NotificationManager::class.java)

  fun handleNotificationClick(
    intent: Intent,
  ) {
    val notiId = intent.getIntExtra(EXTRA_KEY_NOTI_ID, -1)
    if (isFcmServiceNotification(notiId)) {
      cancelFcmServiceNotificationJob()
    }
  }

  fun handleIntentAction(
    intent: Intent?,
    isActivityInForeground: Boolean = false
  ) {
    debugLog("handleIntentAction: $intent")

    if (intent == null) {
      splashController.skipSplash(false)
      return
    }
    val screenNode = IntentCompat.getParcelableExtra(
      intent,
      EXTRA_KEY_NOTI_NAV_SCREEN_NODE,
      ScreenNode::class.java
    ) ?: return

    activityStack.finish<SignaturePadActivity>()

    debugLog("screenNode: ${screenNode.tag()}")

    val notiId = intent.getIntExtra(EXTRA_KEY_NOTI_ID, -1)
    val clickEventRecord = intent.getStringArrayExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORD)

    clickEventRecord?.filterNotNull()?.forEach {
      logEventRecord(it)
    }

    if (notiId != -1) {
      androidNotificationManager?.cancel(notiId)
    }

    val isFinalScreenNodeToHome = screenNode is HomeNode


    when {
      isFinalScreenNodeToHome && isActivityInForeground -> {
        GlobalNavigator.tryTransaction {
          setRoot(screenNode)
          popToRoot()
        }
      }

      isFinalScreenNodeToHome && !isActivityInForeground -> {
        GlobalNavigator.tryTransaction {
          setRoot(screenNode)
          popToRoot()
        }

        activityStack.top()?.let {
          if (it is SplashActivity) return@let
          it.startActivity(SplashActivity.createIntent(it, type = SplashLaunchType.WARM_START))
        }
      }

      !isFinalScreenNodeToHome && isActivityInForeground -> {
        GlobalNavigator.tryTransaction {
          push(screenNode)
        }
      }

      !isFinalScreenNodeToHome && !isActivityInForeground -> {
        GlobalNavigator.tryTransaction {
          push(screenNode)
        }

        activityStack.top()?.let {
          if (it is SplashActivity) return@let
          it.startActivity(SplashActivity.createIntent(it, type = SplashLaunchType.WARM_START))
        }
      }

      else -> {}
    }
  }

  fun createNavigateIntent(
    context: Context,
    notificationId: Int?,
    screenNode: ScreenNode?,
    vararg clickEventRecord: String?,
    flags: Int = pendingIntentDefaultFlags,
  ): PendingIntent {
    val navIntent = SplashActivity.createIntent(context, type = SplashLaunchType.NOTIFICATION_CLICK).apply {
      putExtra(EXTRA_KEY_NOTI_NAV_SCREEN_NODE, screenNode)
      putExtra(EXTRA_KEY_NOTI_ID, notificationId)
      putExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORD, clickEventRecord)
    }

    return PendingIntent.getActivity(
      context,
      notificationId ?: Random(100).nextInt(),
      navIntent,
      flags
    )
  }

  fun createNavigateIntent(
    context: Context,
    notificationId: Int?,
    screenNode: ScreenNode?,
    clickEventRecord: String?,
    flags: Int = pendingIntentDefaultFlags,
  ) = createNavigateIntent(
    context = context,
    notificationId = notificationId,
    screenNode = screenNode,
    clickEventRecord = arrayOf(clickEventRecord),
    flags = flags
  )
}

val pendingIntentDefaultFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
  PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
} else {
  PendingIntent.FLAG_UPDATE_CURRENT
}