@file:Suppress("PropertyName")

package com.example.pdf.biz.bi

import kotlinx.serialization.Serializable

@Serializable
data class BiRecordEvent(
  val event_name: String,
  val activity: String,
  val type: Int
)

@Serializable
data class BiRecordValue(
  val value: Float,
  val currency: String,
  val precision_type: String,
  val ad_network: String,
  val ad_type: String,
  val ad_placement: String,
)

@Serializable
data class BiRecordCkEvent(
  val event_name: String,
  val extra_one: String? = null,
  val extra_two: String? = null,
  val extra_three: String? = null,
)