package com.example.pdf.ui.activity.splash

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.pdf.AppActivity
import com.example.pdf.android.context.findActivity
import com.example.pdf.appActivity
import com.example.pdf.biz.PrefStore
import com.example.pdf.biz.ad.appopen.AdmobAppOpenAdManager
import com.example.pdf.biz.ad.appopen.MaxAppOpenAdHelper
import com.example.pdf.biz.ad.appopen.TryToShowExecuteResult
import com.example.pdf.biz.ad.appopen.closeSplashEventFlow
import com.example.pdf.biz.ad.banner.AdmobBannerAdManager
import com.example.pdf.biz.ad.interstitial.AdmobInterstitialAdManager
import com.example.pdf.biz.ad.interstitial.MaxInterstitialAdHelper
import com.example.pdf.biz.notification.NotificationActionNavigator
import com.example.pdf.biz.notification.NotificationActionNavigator.handleNotificationClick
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.biz.skipSplash
import com.example.pdf.biz.ump.UserMessagingPlatformManager
import com.example.pdf.configureMaxAdIfNeeded
import com.example.pdf.flow.send
import com.example.pdf.kermit.debugLog
import com.example.pdf.ui.activity.SplashLaunchType
import com.example.pdf.ui.activity.splashActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class SplashViewModel(
  @InjectedParam private val launchType: SplashLaunchType,
  private val prefStore: PrefStore,
  private val maxAppOpenAdHelper: MaxAppOpenAdHelper,
  private val maxInterstitialAdHelper: MaxInterstitialAdHelper,
  private val admobAppOpenAdManager: AdmobAppOpenAdManager,
  private val admobInterstitialAdManager: AdmobInterstitialAdManager,
  private val admobBannerAdManager: AdmobBannerAdManager,
  private val remoteConfig: RealRemoteConfig,
  private val userMessagingPlatformManager: UserMessagingPlatformManager
) : ViewModel(), ContainerHost<SplashViewState, SplashSideEffect> {

  override val container: Container<SplashViewState, SplashSideEffect> =
    container(SplashViewState())

  init {
    splashActivity?.intent?.let(NotificationActionNavigator::handleNotificationClick)
  }

  private var hasRequestConsentAndConfigureAds = false

  fun requestConsentAndConfigureAds(
    activity: Activity,
    scope: CoroutineScope
  ) {
    debugLog(tag = "SplashViewModel") { "requestConsentAndConfigureAds() hasRequestConsentAndConfigureAds: $hasRequestConsentAndConfigureAds" }
    if (hasRequestConsentAndConfigureAds) return

    debugLog(tag = "SplashViewModel") { "requestConsentAndConfigureAds() call in" }
//    userMessagingPlatformManager.requestConsentInfoUpdate(activity) {
//      val useLegacyAdConfig = remoteConfig.useLegacyAdConfig
//
//      registerAdFlow(useLegacyAdConfig, scope)
//      onLoadAds(useLegacyAdConfig, activity)
//
//      GlobalScope.launch {
//        configureMaxAdIfNeeded(activity.applicationContext)
//      }
//    }
    val useLegacyAdConfig = remoteConfig.useLegacyAdConfig

    registerAdFlow(useLegacyAdConfig, scope)
    onLoadAds(useLegacyAdConfig, activity)

    GlobalScope.launch {
      configureMaxAdIfNeeded(activity.applicationContext)
    }

    hasRequestConsentAndConfigureAds = true
  }

  private fun registerAdFlow(
    useLegacyAdConfig: Boolean,
    scope: CoroutineScope
  ) {
    if (useLegacyAdConfig) {
      launchedEffectMaxAppOpenAdHandler(scope)
    } else {
      configureAdmobAppOpenAd()
    }
  }

  private fun onLoadAds(
    useLegacyAdConfig: Boolean,
    context: Context,
  ) {
    viewModelScope.launch(Dispatchers.Main.immediate) {
      if (useLegacyAdConfig) {
        maxAppOpenAdHelper.tryToLoadAd()
        maxInterstitialAdHelper.tryToLoadAd()
      } else {
        admobInterstitialAdManager.tryToLoadAd(context.findActivity())
        admobAppOpenAdManager.tryToShowAd(context.findActivity())
      }
    }
  }

  private fun configureAdmobAppOpenAd() {
    admobAppOpenAdManager.adLoadingStateEventFlow.onEach {
      when (it) {
        AdmobAppOpenAdManager.AdLoadingStateEvent.FailedToLoad -> {
          delay(1000)
          onNavUp()
        }

        AdmobAppOpenAdManager.AdLoadingStateEvent.TimeOut -> {
          onNavUp()
        }

        AdmobAppOpenAdManager.AdLoadingStateEvent.Loaded -> {
          onShowAdmobAppOpenAd()
        }
      }
    }.launchIn(viewModelScope)


    admobAppOpenAdManager.adShowStateEventFlow.onEach {
      when (it) {
        AdmobAppOpenAdManager.AdShowStateEvent.FailedToShow,
        AdmobAppOpenAdManager.AdShowStateEvent.Finish -> {
          onNavUp()
        }

        AdmobAppOpenAdManager.AdShowStateEvent.SkipToShow -> {
          delay(1000)
          onNavUp()
        }

        AdmobAppOpenAdManager.AdShowStateEvent.Showing -> {
          intent {
            delay(500)
            reduce { state.copy(appOpenAdShowing = true) }
          }
        }

      }
    }.launchIn(viewModelScope)
  }

  private fun onShowAdmobAppOpenAd() = intent {
    postSideEffect(SplashSideEffect.ShowAdmobAppOpenAd)
  }

  private fun onNavUp() = intent {
    val handleIntentAction = suspend {
      debugLog(tag = "SplashViewModel") { "onNavUp() handleIntentAction" }
      withContext(Dispatchers.Main.immediate) {
        NotificationActionNavigator.handleIntentAction(
          intent = splashActivity?.intent,
          isActivityInForeground = true
        )
      }
    }

    debugLog(tag = "SplashViewModel") { "onNavUp() launchType: $launchType" }

    when (launchType) {
      // real cold launch & launch by launcher
      SplashLaunchType.COLD_START -> {
        withContext(Dispatchers.Main.immediate) {
          if (appActivity == null) {
            debugLog(tag = "SplashViewModel") { "real COLD_START" }
            splashActivity?.let {
              it.startActivity(Intent(it, AppActivity::class.java))
            }
          } else {
            skipSplash()
            debugLog(tag = "SplashViewModel") { "launch by launcher" }
          }
        }
      }

      SplashLaunchType.WARM_START -> {
        skipSplash()
      }

      SplashLaunchType.NOTIFICATION_CLICK -> {
        if (appActivity == null) {
          debugLog(tag = "SplashViewModel") { "NOTIFICATION_CLICK onNavUp() appActivity == null" }

          withContext(Dispatchers.Main.immediate) {
            splashActivity?.let {
              it.startActivity(Intent(it, AppActivity::class.java))
              handleIntentAction()
            }
          }
        } else {
          debugLog(tag = "SplashViewModel") { "NOTIFICATION_CLICK onNavUp() appActivity != null" }

          handleIntentAction()
          skipSplash()
        }
      }
    }


    postSideEffect(SplashSideEffect.NavUp)
  }

  private fun registerMaxAppOpenAdHandler(coroutineScope: CoroutineScope) {
    val timeoutJob = coroutineScope.launch {
      maxAppOpenAdHelper.instantLoadTimeoutDelay()
      maxAppOpenAdHelper.tryToShowExecuteResultEventFlow.send(null)
      onNavUp()
    }

    maxAppOpenAdHelper.tryToShowExecuteResultEventFlow.onEach {
      when (it) {
        TryToShowExecuteResult.DoNotShow,
        TryToShowExecuteResult.Error -> {
          delay(1500)
          onNavUp()
        }

        TryToShowExecuteResult.ShowFinish -> {
          onNavUp()
        }

        TryToShowExecuteResult.ReadyToShow -> {
          timeoutJob.cancel()
        }

        else -> {}
      }
    }.launchIn(coroutineScope)
  }

  fun launchedEffectMaxAppOpenAdHandler(coroutineScope: CoroutineScope) {
    registerMaxAppOpenAdHandler(coroutineScope)
    closeSplashEventFlow.onEach {
      maxAppOpenAdHelper.tryToShowAd()
    }.launchIn(coroutineScope)
  }

  override fun onCleared() {
    maxAppOpenAdHelper.tryToShowExecuteResultEventFlow.send(null)
    super.onCleared()

    debugLog(tag = "SplashViewModel") { "onCleared()" }
  }
}