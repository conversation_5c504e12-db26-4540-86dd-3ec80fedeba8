package com.example.pdf.biz.fcm

import androidx.annotation.DrawableRes
import androidx.compose.ui.graphics.Color
import com.example.pdf.R
import com.example.pdf.guia.ScreenNode
import com.example.pdf.ui.node.home.HomeNode
import com.example.pdf.ui.node.home.HomeTab
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Suppress("PropertyName")
@Serializable
data class FcmServiceMessage(
  @EncodeDefault val id: Int = 0,
  @EncodeDefault val title: String = "",
  @EncodeDefault val title_color: ULong = defaultTitleColor.value,
  @EncodeDefault val title_color_darkmode: ULong = defaultTitleColorDarkMode.value,
  @EncodeDefault val content: String = "",
  @EncodeDefault val content_color: ULong = defaultContentColor.value,
  @EncodeDefault val content_color_darkmode: ULong = defaultContentColorDarkMode.value,
  @EncodeDefault val destination: ClickActionDestination = ClickActionDestination.All,
  @EncodeDefault val type: String = "",
  @EncodeDefault val image_index: Int = 0,
  @EncodeDefault val additional_push_count: Int = 0,
) {
  val destinationNode: ScreenNode
    get() = when (destination) {
      ClickActionDestination.All -> HomeNode(tab = HomeTab.ALL)
      ClickActionDestination.Tools -> HomeNode(tab = HomeTab.TOOLS)
      ClickActionDestination.Bookmarks -> HomeNode(tab = HomeTab.BOOKMARKS)
    }

  @get:DrawableRes
  val imgRes: Int
    get() = when (image_index) {
      0 -> R.drawable.img_fcm_service_message_0
      1 -> R.drawable.img_fcm_service_message_1
      2 -> R.drawable.img_fcm_service_message_2
      3 -> R.drawable.img_fcm_service_message_3
      4 -> R.drawable.img_fcm_service_message_4
      5 -> R.drawable.img_fcm_service_message_5
      6 -> R.drawable.img_fcm_service_message_6
      7 -> R.drawable.img_fcm_service_message_7
      else -> R.drawable.img_fcm_service_message_0
    }

  val titleColor: Color
    get() = try {
      Color(title_color)
    } catch (e: Exception) {
      defaultTitleColor
    }

  val titleColorDarkMode: Color
    get() = try {
      Color(title_color_darkmode)
    } catch (e: Exception) {
      defaultTitleColorDarkMode
    }

  val contentColor: Color
    get() = try {
      Color(content_color)
    } catch (e: Exception) {
      defaultContentColor
    }

  val contentColorDarkMode: Color
    get() = try {
      Color(content_color_darkmode)
    } catch (e: Exception) {
      defaultContentColorDarkMode
    }


  @Serializable
  enum class ClickActionDestination {
    @SerialName("all")
    All,

    @SerialName("tools")
    Tools,

    @SerialName("bookmarks")
    Bookmarks,
  }

  companion object {
    val defaultTitleColor = Color.Black
    val defaultTitleColorDarkMode = Color.White
    val defaultContentColor = Color.Gray
    val defaultContentColorDarkMode = Color.LightGray
  }
}


