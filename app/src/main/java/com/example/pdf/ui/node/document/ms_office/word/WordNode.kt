package com.example.pdf.ui.node.document.ms_office.word

import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import com.bhuvaneshw.pdf.compose.rememberPdfState
import com.bhuvaneshw.pdf.compose.ui.PdfScrollBar
import com.bhuvaneshw.pdf.compose.ui.PdfViewer
import com.bhuvaneshw.pdf.compose.ui.PdfViewerContainer
import com.example.pdf.android.file.document_converter.DocumentConverter
import com.example.pdf.android.share.shareFile
import com.example.pdf.appActivity
import com.example.pdf.appContext
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.example.pdf.ui.feature.loading.LoadingPanel
import com.example.pdf.ui.feature.ms_office.MSOfficeViewer
import com.example.pdf.ui.node.document_details.DocumentDetailsNode
import com.example.pdf.ui.node.document.CommonDocumentUiModel
import com.example.pdf.ui.node.document.CommonDocumentUiState
import com.example.pdf.ui.node.document.CommonDocumentTopBar
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Parcelize
class WordNode(
  override val document: File
) : DocumentNode("word") {
  @Composable
  override fun Content(navigator: Navigator) {
    super.Content(navigator)

    val cdUiModel: CommonDocumentUiModel = koinUiModel { parametersOf(document) }
    val cdUiState by cdUiModel.collectAsState()

    val (_, onBackAction)
      = interstitialAdRegister(navigator)

    WordConvertToPdfImpl(
      document = document,
      commonDocumentUiState = cdUiState,
      onBack = onBackAction,
      onBookmarkClick = { cdUiModel.onBookmarkClick(document, it) },
      onShareClick = { appActivity?.shareFile(File(document.absolutePath)) },
      onMoreClick = { navigator.push(DocumentDetailsNode(document)) }
    )
  }
}

@Composable
private fun WordContent(
  document: File,
  commonDocumentUiState: CommonDocumentUiState,
  onBack: () -> Unit,
  onBookmarkClick: (Boolean) -> Unit,
  onShareClick: () -> Unit,
  onMoreClick: () -> Unit
) {
  Scaffold(
    topBar = {
      CommonDocumentTopBar(
        title = "",
        isBookmarked = commonDocumentUiState.isBookmarked,
        onBackClick = onBack,
        onBookmarkClick = onBookmarkClick,
        onShareClick = onShareClick,
        onMoreClick = onMoreClick
      )
    },
    bottomBar = {
      BannerAd(BannerAdPlace.Document, modifier = Modifier.navigationBarsPadding())
    }
  ) {
    MSOfficeViewer(
      document = document,
      modifier = Modifier
        .fillMaxSize()
        .padding(it)
    )
  }
}


@Composable
private fun WordConvertToPdfImpl(
  document: File,
  commonDocumentUiState: CommonDocumentUiState,
  onBack: () -> Unit,
  onBookmarkClick: (Boolean) -> Unit,
  onShareClick: () -> Unit,
  onMoreClick: () -> Unit
) {
  val documentConverter: DocumentConverter = koinInject()

  var convertedFilePath by remember { mutableStateOf<String?>(null) }

  LaunchedEffect(Unit) {
    val finalFile = File(appContext.cacheDir, "preview_converted.pdf")
    convertedFilePath =
      documentConverter.convertDocToPdfByAspose(document.absolutePath, finalFile.absolutePath)
        .let {
          if (it) {
            finalFile.absolutePath
          } else null
        }
  }

  WordConvertToPdfContent(
    convertedDocumentPath = convertedFilePath,
    commonDocumentUiState = commonDocumentUiState,
    onBack = onBack,
    onBookmarkClick = onBookmarkClick,
    onShareClick = onShareClick,
    onMoreClick = onMoreClick
  )

}

@Composable
private fun WordConvertToPdfContent(
  convertedDocumentPath: String?,
  commonDocumentUiState: CommonDocumentUiState,
  onBack: () -> Unit,
  onBookmarkClick: (Boolean) -> Unit,
  onShareClick: () -> Unit,
  onMoreClick: () -> Unit
) {
  Scaffold(
    topBar = {
      CommonDocumentTopBar(
        title = "",
        isBookmarked = commonDocumentUiState.isBookmarked,
        onBackClick = onBack,
        onBookmarkClick = onBookmarkClick,
        onShareClick = onShareClick,
        onMoreClick = onMoreClick
      )
    },
    bottomBar = {
      BannerAd(BannerAdPlace.Document, modifier = Modifier.navigationBarsPadding())
    }
  ) {
    Crossfade(convertedDocumentPath != null) { convertedDocumentPathNotNull ->
      if (convertedDocumentPathNotNull) {
        requireNotNull(convertedDocumentPath)
        val pdfState = rememberPdfState(source = convertedDocumentPath)

        PdfViewerContainer(
          pdfState = pdfState,
          pdfViewer = {
            PdfViewer()
          },
          pdfToolBar = null,
          pdfScrollBar = { parentSize ->
            PdfScrollBar(
              parentSize = parentSize
            )
          },
          modifier = Modifier
            .fillMaxSize()
            .padding(it)
        )
      } else {
        LoadingPanel(
          modifier = Modifier
            .background(AppTheme.colors.disabled.copy(.3f))
            .fillMaxSize()
            .padding(it),
        )
      }
    }
  }
}